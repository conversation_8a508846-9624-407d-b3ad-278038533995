<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

trait QueryFilterableTrait
{
    /**
     * Apply search filter to query
     */
    protected function applySearchFilter(Builder $query, Request $request, string $requestField = 'name', ?string $field = null): Builder
    {
        $field = $field ?? $requestField;
        $searchTerm = $request->input($requestField);

        if ($searchTerm) {
            $query->where(function ($q) use ($searchTerm, $field) {
                $q->where($field, 'like', "%{$searchTerm}%");
            });
        }

        return $query;
    }

    /**
     * Apply sorting to query
     */
    protected function applySorting(Builder $query, Request $request, string $defaultField = 'created_at', string $defaultDirection = 'desc'): Builder
    {
        $sortField = $request->input('sort_field', $defaultField);
        $sortDirection = $request->input('sort_direction', $defaultDirection);

        if (str_contains($sortField, ':')) {
            [$relation, $relationField] = explode(':', $sortField);

            $model = $query->getModel();
            $tableName = $model->getTable();

            if (method_exists($model, $relation)) {
                $relationObj = $model->$relation();

                if ($relationObj instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo) {
                    $foreignKey = $relationObj->getForeignKeyName();
                    $relatedTable = $relationObj->getRelated()->getTable();

                    if ($relatedTable === $tableName) {
                        $relatedTable = "{$relation}s";
                        $query->leftJoin("{$tableName} as {$relatedTable}", "{$tableName}.{$foreignKey}", '=', "{$relatedTable}.id");
                    } else {
                        $query->leftJoin($relatedTable, "{$tableName}.{$foreignKey}", '=', "{$relatedTable}.id");
                    }

                    $query->select("{$tableName}.*")
                        ->orderBy("{$relatedTable}.{$relationField}", $sortDirection);

                } elseif ($relationObj instanceof \Illuminate\Database\Eloquent\Relations\MorphTo) {
                    // Handle morphTo relationships using subqueries
                    $this->applySortingForMorphTo($query, $relationObj, $relationField, $sortDirection, $tableName);
                } else {
                    $query->orderBy($defaultField, $sortDirection);
                }
            } else {
                $query->orderBy($defaultField, $sortDirection);
            }

            return $query;
        }

        return $query->orderBy($sortField, $sortDirection);
    }

    /**
     * Apply pagination to query
     */
    protected function applyPagination(Builder $query, Request $request, int $defaultPerPage = 10, ?callable $transformer = null): LengthAwarePaginator
    {
        $perPage = $request->input('per_page', $defaultPerPage);

        $paginator = $query->paginate($perPage)->withQueryString();

        if ($transformer) {
            return $paginator->through($transformer);
        }

        return $paginator;
    }

    /**
     * Apply status filter to query
     */
    protected function applyStatusFilter(Builder $query, Request $request, string $statusField = 'status'): Builder
    {
        return $query->when($request->filled($statusField), function ($query) use ($request, $statusField) {
            $query->where($statusField, $request->input($statusField));
        });
    }

    /**
     * Apply relation filter to query
     *
     * @param  Builder  $query  The query builder instance
     * @param  Request  $request  The request object
     * @param  string  $requestField  The request field to check
     * @param  string  $relation  The relation path to filter on
     * @param  string  $field  The field in the relation to filter
     * @param  string  $operator  The comparison operator (default: 'like')
     * @param  bool  $exactMatch  Whether to use exact matching or partial matching
     */
    protected function applyRelationFilter(
        Builder $query,
        Request $request,
        string $requestField,
        string $relation,
        string $field,
        string $operator = 'like',
        bool $exactMatch = false
    ): Builder {
        if ($request->filled($requestField)) {
            $value = $request->input($requestField);

            if ($operator === 'like' && ! $exactMatch) {
                $value = "%{$value}%";
            }

            $query->whereHas($relation, fn ($q) => $q->where($field, $operator, $value));
        }

        return $query;
    }

    /**
     * Apply nested relation filter to query
     *
     * @param  Builder  $query  The query builder instance
     * @param  Request  $request  The request object
     * @param  string  $requestField  The request field to check
     * @param  string  $modelType  The model type to filter on
     * @param  string  $relationPath  The relation path from model to target (dot notation)
     * @param  string  $field  The field in the final relation to filter
     * @param  array  $additionalFields  Additional fields to include in the OR condition
     */
    protected function applyNestedRelationFilter(
        Builder $query,
        Request $request,
        string $requestField,
        string $modelType,
        string $relationPath,
        string $field,
        array $additionalFields = []
    ): Builder {
        if ($request->filled($requestField)) {
            $value = $request->input($requestField);

            $query->whereHas('associatedModels', function ($query) use ($modelType, $relationPath, $field, $value, $additionalFields) {
                $query->where('model_type', $modelType)
                    ->whereHas('model', function ($query) use ($relationPath, $field, $value, $additionalFields) {
                        $relations = explode('.', $relationPath);
                        $lastRelation = array_pop($relations);

                        $nestedQuery = $query;
                        foreach ($relations as $relation) {
                            $nestedQuery = $nestedQuery->whereHas($relation, function ($q) {
                                return $q;
                            });
                        }

                        $nestedQuery->whereHas($lastRelation, function ($query) use ($field, $value, $additionalFields) {
                            $query->where($field, 'like', "%{$value}%");

                            foreach ($additionalFields as $additionalField) {
                                $query->orWhere($additionalField, 'like', "%{$value}%");
                            }
                        });
                    });
            });
        }

        return $query;
    }

    /**
     * Apply sorting for morphTo relationships using subqueries
     *
     * @param  Builder  $query  The query builder instance
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo  $relationObj  The morphTo relationship
     * @param  string  $relationField  The field to sort by in the related model
     * @param  string  $sortDirection  The sort direction (asc/desc)
     * @param  string  $tableName  The base table name
     */
    protected function applySortingForMorphTo(
        Builder $query,
        \Illuminate\Database\Eloquent\Relations\MorphTo $relationObj,
        string $relationField,
        string $sortDirection,
        string $tableName
    ): void {
        $morphType = $relationObj->getMorphType();
        $morphId = $relationObj->getForeignKeyName();

        // Get all possible morph types from the database
        $morphTypes = $query->getModel()->newQuery()
            ->select($morphType)
            ->distinct()
            ->whereNotNull($morphType)
            ->pluck($morphType)
            ->toArray();

        if (empty($morphTypes)) {
            $query->orderBy($relationField, $sortDirection);
            return;
        }

        // Create a CASE statement for sorting
        $caseStatement = 'CASE ';

        foreach ($morphTypes as $type) {
            if (!class_exists($type)) {
                continue;
            }

            $relatedModel = new $type;
            $relatedTable = $relatedModel->getTable();

            $caseStatement .= "WHEN {$tableName}.{$morphType} = '{$type}' THEN (
                SELECT {$relatedTable}.{$relationField}
                FROM {$relatedTable}
                WHERE {$relatedTable}.id = {$tableName}.{$morphId}
            ) ";
        }

        $caseStatement .= 'END';

        $query->select("{$tableName}.*")
            ->orderByRaw("{$caseStatement} {$sortDirection}");
    }

    /**
     * Apply sorting for morphTo relationships using union approach (alternative method)
     *
     * @param  Builder  $query  The query builder instance
     * @param  \Illuminate\Database\Eloquent\Relations\MorphTo  $relationObj  The morphTo relationship
     * @param  string  $relationField  The field to sort by in the related model
     * @param  string  $sortDirection  The sort direction (asc/desc)
     * @param  string  $tableName  The base table name
     * @param  array  $allowedTypes  Specific morph types to include (optional)
     */
    protected function applySortingForMorphToWithUnion(
        Builder $query,
        \Illuminate\Database\Eloquent\Relations\MorphTo $relationObj,
        string $relationField,
        string $sortDirection,
        string $tableName,
        array $allowedTypes = []
    ): void {
        $morphType = $relationObj->getMorphType();
        $morphId = $relationObj->getForeignKeyName();

        // If specific types are provided, use them; otherwise get from database
        if (empty($allowedTypes)) {
            $allowedTypes = $query->getModel()->newQuery()
                ->select($morphType)
                ->distinct()
                ->whereNotNull($morphType)
                ->pluck($morphType)
                ->toArray();
        }

        if (empty($allowedTypes)) {
            $query->orderBy($relationField, $sortDirection);
            return;
        }

        // Create a derived table with all possible sort values
        $unionQuery = null;

        foreach ($allowedTypes as $type) {
            if (!class_exists($type)) {
                continue;
            }

            $relatedModel = new $type;
            $relatedTable = $relatedModel->getTable();

            $subQuery = $query->getModel()->newQuery()
                ->select([
                    "{$tableName}.id as base_id",
                    "{$relatedTable}.{$relationField} as sort_field"
                ])
                ->from($tableName)
                ->join($relatedTable, function($join) use ($tableName, $relatedTable, $morphType, $morphId, $type) {
                    $join->on("{$tableName}.{$morphId}", '=', "{$relatedTable}.id")
                         ->where("{$tableName}.{$morphType}", '=', $type);
                });

            if ($unionQuery === null) {
                $unionQuery = $subQuery;
            } else {
                $unionQuery->union($subQuery);
            }
        }

        if ($unionQuery) {
            $query->select("{$tableName}.*")
                ->leftJoinSub($unionQuery, 'morph_sort', "{$tableName}.id", '=', 'morph_sort.base_id')
                ->orderBy('morph_sort.sort_field', $sortDirection);
        }
    }
}
