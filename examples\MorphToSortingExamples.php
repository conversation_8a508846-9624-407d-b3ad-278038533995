<?php

namespace Examples;

use App\Models\Address;
use App\Models\Contact;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\Request;

/**
 * Examples of how to use morphTo sorting with QueryFilterableTrait
 */
class MorphToSortingExamples
{
    use QueryFilterableTrait;

    /**
     * Example 1: Sort addresses by the name of their addressable entity
     * Usage: ?sort_field=addressable:name&sort_direction=asc
     */
    public function sortAddressesByAddressableName(Request $request)
    {
        $query = Address::query();
        
        // This will now work with morphTo relationships
        $this->applySorting($query, $request, 'created_at', 'desc');
        
        return $query->paginate(15);
    }

    /**
     * Example 2: Sort contacts by their contactable entity's name
     * Usage: ?sort_field=contactable:name&sort_direction=asc
     */
    public function sortContactsByContactableName(Request $request)
    {
        $query = Contact::query();
        
        $this->applySorting($query, $request, 'created_at', 'desc');
        
        return $query->paginate(15);
    }

    /**
     * Example 3: Manual usage of morphTo sorting with specific types
     */
    public function manualMorphToSorting(Request $request)
    {
        $query = Address::query();
        
        // Get the morphTo relationship
        $addressModel = new Address();
        $addressableRelation = $addressModel->addressable();
        
        // Apply sorting manually with specific allowed types
        $this->applySortingForMorphToWithUnion(
            $query,
            $addressableRelation,
            'name', // field to sort by
            'asc',  // direction
            'addresses', // table name
            [
                \App\Models\Company::class,
                \App\Models\Team::class,
                \App\Models\Collateral::class
            ]
        );
        
        return $query->get();
    }

    /**
     * Example 4: Complex sorting with additional filters
     */
    public function complexMorphToSorting(Request $request)
    {
        $query = Address::query()
            ->where('is_primary', true)
            ->whereIn('addressable_type', [
                \App\Models\Company::class,
                \App\Models\Team::class
            ]);
        
        // Apply morphTo sorting
        $this->applySorting($query, $request, 'created_at', 'desc');
        
        // Apply additional filters
        $this->applySearchFilter($query, $request, 'city');
        
        return $this->applyPagination($query, $request, 10);
    }

    /**
     * Example 5: Sorting with error handling
     */
    public function safeMorphToSorting(Request $request)
    {
        $query = Address::query();
        
        try {
            $this->applySorting($query, $request, 'created_at', 'desc');
        } catch (\Exception $e) {
            // Fallback to default sorting if morphTo sorting fails
            $query->orderBy('created_at', 'desc');
            
            // Log the error for debugging
            \Log::warning('MorphTo sorting failed', [
                'error' => $e->getMessage(),
                'sort_field' => $request->input('sort_field'),
                'sort_direction' => $request->input('sort_direction')
            ]);
        }
        
        return $query->paginate(15);
    }
}
