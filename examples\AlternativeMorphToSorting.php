<?php

namespace Examples;

use App\Models\Address;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

/**
 * Alternative approaches for morphTo sorting
 */
class AlternativeMorphToSorting
{
    /**
     * Approach 2: Collection-based sorting (good for smaller datasets)
     */
    public function collectionBasedSorting(Request $request)
    {
        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $perPage = $request->input('per_page', 15);
        $page = $request->input('page', 1);

        // Get all addresses with their morphTo relationships loaded
        $addresses = Address::with('addressable')->get();

        // Sort using collection methods
        if (str_contains($sortField, ':')) {
            [$relation, $relationField] = explode(':', $sortField);
            
            if ($relation === 'addressable') {
                $sorted = $addresses->sortBy(function ($address) use ($relationField) {
                    return $address->addressable?->{$relationField} ?? '';
                }, SORT_REGULAR, $sortDirection === 'desc');
            } else {
                $sorted = $addresses->sortBy($sortField, SORT_REGULAR, $sortDirection === 'desc');
            }
        } else {
            $sorted = $addresses->sortBy($sortField, SORT_REGULAR, $sortDirection === 'desc');
        }

        // Manual pagination
        $total = $sorted->count();
        $items = $sorted->forPage($page, $perPage)->values();

        return new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
            ['path' => request()->url(), 'pageName' => 'page']
        );
    }

    /**
     * Approach 3: Type-specific queries with union
     */
    public function typeSpecificUnionSorting(Request $request)
    {
        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');

        if (!str_contains($sortField, ':')) {
            return Address::orderBy($sortField, $sortDirection)->paginate(15);
        }

        [$relation, $relationField] = explode(':', $sortField);
        
        if ($relation !== 'addressable') {
            return Address::orderBy('created_at', $sortDirection)->paginate(15);
        }

        // Create separate queries for each morphable type
        $companyAddresses = Address::select([
                'addresses.*',
                'companies.name as sort_field'
            ])
            ->join('companies', function($join) {
                $join->on('addresses.addressable_id', '=', 'companies.id')
                     ->where('addresses.addressable_type', '=', \App\Models\Company::class);
            });

        $teamAddresses = Address::select([
                'addresses.*',
                'teams.name as sort_field'
            ])
            ->join('teams', function($join) {
                $join->on('addresses.addressable_id', '=', 'teams.id')
                     ->where('addresses.addressable_type', '=', \App\Models\Team::class);
            });

        $collateralAddresses = Address::select([
                'addresses.*',
                'collaterals.name as sort_field'
            ])
            ->join('collaterals', function($join) {
                $join->on('addresses.addressable_id', '=', 'collaterals.id')
                     ->where('addresses.addressable_type', '=', \App\Models\Collateral::class);
            });

        // Union all queries
        $unionQuery = $companyAddresses
            ->union($teamAddresses)
            ->union($collateralAddresses);

        // Wrap in subquery and apply ordering
        $finalQuery = Address::fromSub($unionQuery, 'sorted_addresses')
            ->orderBy('sort_field', $sortDirection);

        return $finalQuery->paginate(15);
    }

    /**
     * Approach 4: Using raw SQL with dynamic table joins
     */
    public function rawSqlApproach(Request $request)
    {
        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');

        if (!str_contains($sortField, ':')) {
            return Address::orderBy($sortField, $sortDirection)->paginate(15);
        }

        [$relation, $relationField] = explode(':', $sortField);
        
        if ($relation !== 'addressable') {
            return Address::orderBy('created_at', $sortDirection)->paginate(15);
        }

        $sql = "
            SELECT addresses.*, 
                   CASE 
                       WHEN addresses.addressable_type = ? THEN companies.{$relationField}
                       WHEN addresses.addressable_type = ? THEN teams.{$relationField}
                       WHEN addresses.addressable_type = ? THEN collaterals.{$relationField}
                       ELSE NULL
                   END as sort_field
            FROM addresses
            LEFT JOIN companies ON addresses.addressable_id = companies.id 
                                AND addresses.addressable_type = ?
            LEFT JOIN teams ON addresses.addressable_id = teams.id 
                            AND addresses.addressable_type = ?
            LEFT JOIN collaterals ON addresses.addressable_id = collaterals.id 
                                   AND addresses.addressable_type = ?
            ORDER BY sort_field {$sortDirection}
        ";

        $bindings = [
            \App\Models\Company::class,
            \App\Models\Team::class,
            \App\Models\Collateral::class,
            \App\Models\Company::class,
            \App\Models\Team::class,
            \App\Models\Collateral::class,
        ];

        return Address::fromQuery($sql, $bindings);
    }

    /**
     * Approach 5: Cached morphTo sorting for better performance
     */
    public function cachedMorphToSorting(Request $request)
    {
        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $cacheKey = "morph_sort_{$sortField}_{$sortDirection}";

        return cache()->remember($cacheKey, 300, function () use ($sortField, $sortDirection) {
            if (!str_contains($sortField, ':')) {
                return Address::orderBy($sortField, $sortDirection)->paginate(15);
            }

            [$relation, $relationField] = explode(':', $sortField);
            
            if ($relation !== 'addressable') {
                return Address::orderBy('created_at', $sortDirection)->paginate(15);
            }

            // Use the subquery approach with caching
            $query = Address::query();
            $morphType = 'addressable_type';
            $morphId = 'addressable_id';

            $caseStatement = 'CASE ';
            $morphTypes = [\App\Models\Company::class, \App\Models\Team::class, \App\Models\Collateral::class];
            
            foreach ($morphTypes as $type) {
                $relatedModel = new $type;
                $relatedTable = $relatedModel->getTable();
                
                $caseStatement .= "WHEN addresses.{$morphType} = '{$type}' THEN (
                    SELECT {$relatedTable}.{$relationField} 
                    FROM {$relatedTable} 
                    WHERE {$relatedTable}.id = addresses.{$morphId}
                ) ";
            }
            
            $caseStatement .= 'END';
            
            return $query->select('addresses.*')
                ->orderByRaw("{$caseStatement} {$sortDirection}")
                ->paginate(15);
        });
    }
}
